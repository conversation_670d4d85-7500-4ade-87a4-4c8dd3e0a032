{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block admin_title %}User Profile - {{ user_profile.username }}{% endblock %}
{% block page_title %}User Management{% endblock %}
{% block page_description %}Manage user profile and account settings{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/profile-forms.css' %}">
{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{% url 'core:admin_users' %}" class="ml-1 text-sm font-medium text-gray-500 hover:text-gray-700 md:ml-2">Users</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ user_profile.username }}</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Enhanced Modern User Profile Header -->
    <div class="mb-8 relative overflow-hidden animate-fade-in-up">
        <!-- Main Header Card -->
        <div class="bg-gradient-to-br from-harrier-red via-harrier-dark to-harrier-blue rounded-3xl p-8 relative overflow-hidden shadow-2xl">
            <!-- Animated Background Elements -->
            <div class="absolute inset-0 bg-black/10"></div>
            <div class="absolute -top-20 -right-20 w-80 h-80 bg-white/5 rounded-full animate-pulse"></div>
            <div class="absolute -bottom-20 -left-20 w-60 h-60 bg-white/5 rounded-full animate-pulse delay-1000"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-radial from-white/5 to-transparent rounded-full"></div>

            <div class="relative z-10">
                <div class="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-8">
                    <!-- User Information Section -->
                    <div class="flex flex-col lg:flex-row lg:items-center gap-6">
                        <!-- Enhanced Profile Picture -->
                        <div class="relative group">
                            <div class="w-32 h-32 rounded-3xl overflow-hidden bg-white/10 backdrop-blur-md border-2 border-white/20 shadow-2xl group-hover:scale-105 transition-all duration-300">
                                {% if user_profile.profile_picture %}
                                    <img src="{{ user_profile.profile_picture.url }}" alt="{{ user_profile.get_full_name }}"
                                         class="w-full h-full object-cover">
                                {% else %}
                                    <div class="w-full h-full flex items-center justify-center text-white text-3xl font-bold font-montserrat bg-gradient-to-br from-white/20 to-white/10">
                                        {{ user_profile.first_name|first|default:user_profile.username|first|upper }}{{ user_profile.last_name|first|upper }}
                                    </div>
                                {% endif %}
                            </div>
                            <!-- Status Indicator -->
                            <div class="absolute -bottom-2 -right-2 w-10 h-10 {% if user_profile.is_active %}bg-green-500{% else %}bg-red-500{% endif %} rounded-2xl border-4 border-white flex items-center justify-center shadow-lg">
                                <i class="fas fa-{% if user_profile.is_active %}check{% else %}times{% endif %} text-white text-sm"></i>
                            </div>
                            <!-- Online Status -->
                            {% if user_profile.is_active %}
                            <div class="absolute -top-2 -left-2 w-6 h-6 bg-green-400 rounded-full border-2 border-white animate-ping"></div>
                            <div class="absolute -top-2 -left-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                            {% endif %}
                        </div>

                        <!-- Enhanced User Details -->
                        <div class="text-white space-y-3">
                            <!-- Name and Role -->
                            <div class="space-y-2">
                                <div class="flex flex-wrap items-center gap-3">
                                    <h1 class="text-4xl font-bold font-montserrat bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                                        {{ user_profile.first_name|default:"" }} {{ user_profile.last_name|default:"" }}
                                        {% if not user_profile.first_name and not user_profile.last_name %}
                                            {{ user_profile.username }}
                                        {% endif %}
                                    </h1>
                                    <div class="flex items-center gap-2">
                                        <span class="inline-flex items-center px-4 py-2 rounded-2xl text-sm font-semibold backdrop-blur-md border
                                            {% if user_profile.role == 'admin' %}bg-purple-500/20 text-purple-200 border-purple-400/30
                                            {% elif user_profile.role == 'vendor' %}bg-orange-500/20 text-orange-200 border-orange-400/30
                                            {% else %}bg-blue-500/20 text-blue-200 border-blue-400/30{% endif %}">
                                            <i class="fas fa-{% if user_profile.role == 'admin' %}crown{% elif user_profile.role == 'vendor' %}store{% else %}user{% endif %} mr-2"></i>
                                            {{ user_profile.get_role_display }}
                                        </span>
                                        {% if user_profile.is_email_verified %}
                                        <span class="inline-flex items-center px-3 py-1 rounded-xl text-xs font-medium bg-green-500/20 text-green-200 border border-green-400/30">
                                            <i class="fas fa-shield-check mr-1"></i>Verified
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                                <p class="text-blue-100 text-lg font-raleway">@{{ user_profile.username }}</p>
                                {% if vendor %}
                                <div class="flex items-center gap-2">
                                    <span class="text-blue-100 text-sm">{{ vendor.company_name }}</span>
                                    {% if vendor.is_approved %}
                                        <span class="inline-flex items-center px-2 py-1 bg-green-500/20 rounded-full text-green-200 text-xs">
                                            <i class="fas fa-shield-check mr-1"></i>Verified Business
                                        </span>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>

                            <!-- Contact Information Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-2">
                                {% if user_profile.email %}
                                <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/20">
                                    <div class="w-8 h-8 bg-blue-500/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-envelope text-blue-200 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs text-blue-200 font-medium">Email</p>
                                        <p class="text-white text-sm">{{ user_profile.email }}</p>
                                    </div>
                                </div>
                                {% endif %}

                                {% if user_profile.phone %}
                                <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/20">
                                    <div class="w-8 h-8 bg-green-500/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-phone text-green-200 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs text-green-200 font-medium">Phone</p>
                                        <p class="text-white text-sm">{{ user_profile.phone }}</p>
                                    </div>
                                </div>
                                {% endif %}

                                {% if user_profile.city %}
                                <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/20">
                                    <div class="w-8 h-8 bg-purple-500/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-map-marker-alt text-purple-200 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-xs text-purple-200 font-medium">Location</p>
                                        <p class="text-white text-sm">{{ user_profile.city }}, {{ user_profile.country }}</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Action Buttons -->
                    <div class="flex flex-col gap-4">
                        <!-- Member Info Card -->
                        <div class="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20">
                            <div class="text-center text-white">
                                <p class="text-xs text-blue-200 font-medium mb-1">Member Since</p>
                                <p class="text-lg font-bold">{{ user_profile.date_joined|date:"M d, Y" }}</p>
                                <p class="text-xs text-blue-200 mt-1">Last login: {{ user_profile.last_login|date:"M d"|default:"Never" }}</p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-3">
                            <form method="post" class="inline-block">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="toggle_status">
                                <button type="submit" class="w-full sm:w-auto px-6 py-3 bg-white/15 hover:bg-white/25 text-white rounded-2xl text-sm font-semibold transition-all duration-300 backdrop-blur-md border border-white/20 hover:border-white/40 hover:scale-105 shadow-lg">
                                    <i class="fas fa-{% if user_profile.is_active %}user-slash{% else %}user-check{% endif %} mr-2"></i>
                                    {% if user_profile.is_active %}Deactivate{% else %}Activate{% endif %}
                                </button>
                            </form>

                            <button type="button" class="w-full sm:w-auto px-6 py-3 bg-white/15 hover:bg-white/25 text-white rounded-2xl text-sm font-semibold transition-all duration-300 backdrop-blur-md border border-white/20 hover:border-white/40 hover:scale-105 shadow-lg" onclick="openPasswordResetModal()">
                                <i class="fas fa-key mr-2"></i>Reset Password
                            </button>
                        </div>

                        <a href="{% url 'core:admin_users' %}" class="w-full text-center px-6 py-3 bg-white/15 hover:bg-white/25 text-white rounded-2xl text-sm font-semibold transition-all duration-300 backdrop-blur-md border border-white/20 hover:border-white/40 hover:scale-105 shadow-lg">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Users
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats Bar -->
        <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-white rounded-2xl p-4 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-800">Member Since</p>
                        <p class="text-lg font-bold text-harrier-dark">{{ user_profile.date_joined|date:"M Y" }}</p>
                    </div>
                    <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                        <i class="fas fa-calendar text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-2xl p-4 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-800">Last Login</p>
                        <p class="text-lg font-bold text-harrier-dark">{{ user_profile.last_login|date:"M d"|default:"Never" }}</p>
                    </div>
                    <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
                        <i class="fas fa-sign-in-alt text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-2xl p-4 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-800">Activities</p>
                        <p class="text-lg font-bold text-harrier-dark">{{ recent_activities.count|default:0 }}</p>
                    </div>
                    <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-2xl p-4 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 group-hover:text-gray-800">Status</p>
                        <p class="text-lg font-bold {% if user_profile.is_active %}text-green-600{% else %}text-red-600{% endif %}">
                            {% if user_profile.is_active %}Active{% else %}Inactive{% endif %}
                        </p>
                    </div>
                    <div class="w-10 h-10 {% if user_profile.is_active %}bg-green-100 group-hover:bg-green-200{% else %}bg-red-100 group-hover:bg-red-200{% endif %} rounded-xl flex items-center justify-center transition-colors">
                        <i class="fas fa-{% if user_profile.is_active %}check-circle{% else %}times-circle{% endif %} {% if user_profile.is_active %}text-green-600{% else %}text-red-600{% endif %}"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Modern Tab Navigation -->
    <div class="mb-8 animate-fade-in-up" style="animation-delay: 0.1s;">
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-2">
            <nav class="flex flex-wrap gap-2" aria-label="Tabs">
                <button type="button" class="modern-tab active" data-tab="profile">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 text-sm"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-semibold">Profile</div>
                            <div class="text-xs text-gray-500">Personal information</div>
                        </div>
                    </div>
                </button>

                <button type="button" class="modern-tab" data-tab="account">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cog text-purple-600 text-sm"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-semibold">Account</div>
                            <div class="text-xs text-gray-500">Settings & permissions</div>
                        </div>
                    </div>
                </button>

                <button type="button" class="modern-tab" data-tab="activity">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-history text-green-600 text-sm"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-semibold">Activity</div>
                            <div class="text-xs text-gray-500">Recent actions</div>
                        </div>
                    </div>
                </button>

                {% if vendor %}
                <button type="button" class="modern-tab" data-tab="business">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-orange-600 text-sm"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-semibold">Business</div>
                            <div class="text-xs text-gray-500">Company details</div>
                        </div>
                    </div>
                </button>
                {% endif %}

                <button type="button" class="modern-tab" data-tab="analytics">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-indigo-600 text-sm"></i>
                        </div>
                        <div class="text-left">
                            <div class="font-semibold">Analytics</div>
                            <div class="text-xs text-gray-500">Performance data</div>
                        </div>
                    </div>
                </button>
            </nav>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8 animate-fade-in-up" style="animation-delay: 0.2s;">
        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Profile Tab -->
            <div class="tab-content active" id="profile-tab">
                <form method="post" enctype="multipart/form-data" class="space-y-8">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="update_profile">
                    
                    <!-- Profile Picture Section -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-harrier-red to-harrier-red-dark">
                                <i class="fas fa-camera text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Profile Picture</h3>
                                <p class="form-section-subtitle">Update user's profile picture</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-6">
                            <div class="relative">
                                {% if user_profile.profile_picture %}
                                    <img class="h-20 w-20 object-cover rounded-xl shadow-lg border-2 border-gray-200" 
                                         src="{{ user_profile.profile_picture.url }}" alt="Profile picture" id="profileImagePreview">
                                {% else %}
                                    <div class="h-20 w-20 bg-gradient-to-br from-harrier-red to-harrier-red-dark rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg" id="profileImagePreview">
                                        {{ user_profile.first_name|first|default:user_profile.username|first|upper }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="flex-1">
                                {{ user_form.profile_picture }}
                                <div class="file-upload-area" onclick="document.getElementById('id_profile_picture').click()">
                                    <i class="fas fa-cloud-upload-alt file-upload-icon"></i>
                                    <div class="file-upload-text">
                                        <p class="font-medium">Click to upload new profile picture</p>
                                        <p class="text-xs">PNG, JPG up to 2MB (Recommended: 400x400px)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Basic Information -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Basic Information</h3>
                                <p class="form-section-subtitle">User's personal details</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ user_form.first_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user mr-1 text-harrier-red"></i>First Name
                                </label>
                                {{ user_form.first_name }}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ user_form.last_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user mr-1 text-harrier-red"></i>Last Name
                                </label>
                                {{ user_form.last_name }}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ user_form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-at mr-1 text-purple-500"></i>Username
                                </label>
                                {{ user_form.username }}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ user_form.email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope mr-1 text-blue-500"></i>Email Address
                                </label>
                                {{ user_form.email }}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ user_form.phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone mr-1 text-green-500"></i>Phone Number
                                </label>
                                {{ user_form.phone }}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ user_form.date_of_birth.id_for_label }}" class="form-label">
                                    <i class="fas fa-birthday-cake mr-1 text-yellow-500"></i>Date of Birth
                                </label>
                                {{ user_form.date_of_birth }}
                            </div>

                            <div class="form-group">
                                <label for="{{ user_form.gender.id_for_label }}" class="form-label">
                                    <i class="fas fa-venus-mars mr-1 text-pink-500"></i>Gender
                                </label>
                                {{ user_form.gender }}
                            </div>

                            <div class="form-group">
                                <label for="{{ user_form.city.id_for_label }}" class="form-label">
                                    <i class="fas fa-city mr-1 text-teal-500"></i>City
                                </label>
                                {{ user_form.city }}
                            </div>

                            <div class="form-group">
                                <label for="{{ user_form.country.id_for_label }}" class="form-label">
                                    <i class="fas fa-flag mr-1 text-red-500"></i>Country
                                </label>
                                {{ user_form.country }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ user_form.bio.id_for_label }}" class="form-label">
                                <i class="fas fa-file-alt mr-1 text-indigo-500"></i>Bio
                            </label>
                            {{ user_form.bio }}
                        </div>

                        <!-- Additional Contact Information -->
                        <div class="form-section form-fade-in">
                            <div class="form-section-header">
                                <div class="form-section-icon bg-gradient-to-br from-green-500 to-green-600">
                                    <i class="fas fa-address-book text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="form-section-title">Additional Contact Information</h3>
                                    <p class="form-section-subtitle">Secondary contact details</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="{{ user_form.secondary_phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone-alt mr-1 text-green-500"></i>Secondary Phone
                                    </label>
                                    {{ user_form.secondary_phone }}
                                </div>

                                <div class="form-group">
                                    <label for="{{ user_form.whatsapp_number.id_for_label }}" class="form-label">
                                        <i class="fab fa-whatsapp mr-1 text-green-500"></i>WhatsApp Number
                                    </label>
                                    {{ user_form.whatsapp_number }}
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="{{ user_form.address.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt mr-1 text-red-500"></i>Address
                                </label>
                                {{ user_form.address }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-4">
                        <button type="button" class="form-button-secondary" onclick="resetForm()">
                            <i class="fas fa-undo mr-2"></i>Reset
                        </button>
                        <button type="submit" class="form-button-primary">
                            <i class="fas fa-save mr-2"></i>Update Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Account Tab -->
            <div class="tab-content" id="account-tab">
                <div class="space-y-8">
                    <!-- Account Information -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-user-cog text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Account Information</h3>
                                <p class="form-section-subtitle">User account details and permissions</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Account Details</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">User ID</span>
                                        <span class="text-sm font-medium">#{{ user_profile.id }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Username</span>
                                        <span class="text-sm font-medium">{{ user_profile.username }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Email</span>
                                        <span class="text-sm font-medium">{{ user_profile.email }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Role</span>
                                        <span class="text-sm font-medium">{{ user_profile.get_role_display }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Date Joined</span>
                                        <span class="text-sm font-medium">{{ user_profile.date_joined|date:"M d, Y" }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Last Login</span>
                                        <span class="text-sm font-medium">{{ user_profile.last_login|date:"M d, Y H:i"|default:"Never" }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Permissions</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Active</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if user_profile.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                            {% if user_profile.is_active %}Yes{% else %}No{% endif %}
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Staff</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if user_profile.is_staff %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                            {% if user_profile.is_staff %}Yes{% else %}No{% endif %}
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Superuser</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if user_profile.is_superuser %}bg-purple-100 text-purple-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                            {% if user_profile.is_superuser %}Yes{% else %}No{% endif %}
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Email Verified</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if user_profile.is_email_verified %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                            {% if user_profile.is_email_verified %}Yes{% else %}No{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Statistics -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-chart-bar text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">User Statistics</h3>
                                <p class="form-section-subtitle">Activity and engagement metrics</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-blue-50 rounded-xl p-6 text-center">
                                <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-shopping-cart text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-blue-600">{{ user_orders.count|default:0 }}</div>
                                <div class="text-sm text-blue-600">Total Orders</div>
                            </div>

                            <div class="bg-green-50 rounded-xl p-6 text-center">
                                <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-green-600">{{ user_inquiries.count|default:0 }}</div>
                                <div class="text-sm text-green-600">Inquiries Sent</div>
                            </div>

                            {% if vendor %}
                            <div class="bg-purple-50 rounded-xl p-6 text-center">
                                <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-car text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-purple-600">{{ user_listings.count|default:0 }}</div>
                                <div class="text-sm text-purple-600">Active Listings</div>
                            </div>
                            {% else %}
                            <div class="bg-orange-50 rounded-xl p-6 text-center">
                                <div class="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-eye text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-orange-600">{{ analytics_data.profile_views_made|default:0 }}</div>
                                <div class="text-sm text-orange-600">Profile Views</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Tab -->
            <div class="tab-content" id="activity-tab">
                <div class="space-y-8">
                    <!-- Recent Activity -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                                <i class="fas fa-history text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Recent Activity</h3>
                                <p class="form-section-subtitle">User's recent actions and events</p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            {% for activity in recent_activities %}
                            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl">
                                <div class="w-10 h-10 bg-harrier-red rounded-full flex items-center justify-center">
                                    <i class="fas fa-circle text-white text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-harrier-dark">{{ activity.get_action_display }}</div>
                                    {% if activity.description %}
                                        <div class="text-sm text-gray-600">{{ activity.description }}</div>
                                    {% endif %}
                                    <div class="text-xs text-gray-500">{{ activity.timestamp|timesince }} ago</div>
                                </div>
                                {% if activity.ip_address %}
                                    <div class="text-xs text-gray-500">{{ activity.ip_address }}</div>
                                {% endif %}
                            </div>
                            {% empty %}
                            <div class="text-center py-8">
                                <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-500">No recent activity found</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Tab (Vendor Only) -->
            {% if vendor %}
            <div class="tab-content" id="business-tab">
                <form method="post" enctype="multipart/form-data" class="space-y-8">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="update_vendor">

                    <!-- Company Information -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-orange-500 to-orange-600">
                                <i class="fas fa-building text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Company Information</h3>
                                <p class="form-section-subtitle">Business details and branding</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ vendor_form.company_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-building mr-1 text-orange-500"></i>Company Name
                                </label>
                                {{ vendor_form.company_name }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.business_license.id_for_label }}" class="form-label">
                                    <i class="fas fa-certificate mr-1 text-blue-500"></i>Business License
                                </label>
                                {{ vendor_form.business_license }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.business_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-industry mr-1 text-purple-500"></i>Business Type
                                </label>
                                {{ vendor_form.business_type }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.year_established.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar mr-1 text-green-500"></i>Year Established
                                </label>
                                {{ vendor_form.year_established }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ vendor_form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-file-alt mr-1 text-indigo-500"></i>Business Description
                            </label>
                            {{ vendor_form.description }}
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-address-book text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Contact Information</h3>
                                <p class="form-section-subtitle">Business contact details</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="{{ vendor_form.business_phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone mr-1 text-green-500"></i>Business Phone
                                </label>
                                {{ vendor_form.business_phone }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.business_email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope mr-1 text-blue-500"></i>Business Email
                                </label>
                                {{ vendor_form.business_email }}
                            </div>

                            <div class="form-group">
                                <label for="{{ vendor_form.website.id_for_label }}" class="form-label">
                                    <i class="fas fa-globe mr-1 text-purple-500"></i>Website
                                </label>
                                {{ vendor_form.website }}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ vendor_form.physical_address.id_for_label }}" class="form-label">
                                <i class="fas fa-map-marker-alt mr-1 text-red-500"></i>Physical Address
                            </label>
                            {{ vendor_form.physical_address }}
                        </div>
                    </div>

                    <!-- Business Status -->
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-shield-check text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Business Status</h3>
                                <p class="form-section-subtitle">Verification and approval status</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Verification Status</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Approved</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if vendor.is_approved %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                            {% if vendor.is_approved %}Yes{% else %}No{% endif %}
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Verification Status</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if vendor.verification_status == 'verified' %}bg-green-100 text-green-800
                                            {% elif vendor.verification_status == 'pending' %}bg-yellow-100 text-yellow-800
                                            {% else %}bg-red-100 text-red-800{% endif %}">
                                            {{ vendor.get_verification_status_display }}
                                        </span>
                                    </div>
                                    {% if vendor.approval_date %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Approved Date</span>
                                        <span class="text-sm font-medium">{{ vendor.approval_date|date:"M d, Y" }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Business Metrics</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Average Rating</span>
                                        <span class="text-sm font-medium">{{ vendor.average_rating|floatformat:1|default:"N/A" }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Total Sales</span>
                                        <span class="text-sm font-medium">{{ vendor.total_sales|default:0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Member Since</span>
                                        <span class="text-sm font-medium">{{ vendor.created_at|date:"M Y" }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" class="form-button-secondary" onclick="resetForm()">
                            <i class="fas fa-undo mr-2"></i>Reset
                        </button>
                        <button type="submit" class="form-button-primary">
                            <i class="fas fa-save mr-2"></i>Update Business Profile
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}

            <!-- Analytics Tab -->
            <div class="tab-content" id="analytics-tab">
                {% if analytics_data %}
                <div class="space-y-8">
                    {% if user_profile.role == 'vendor' and analytics_data.analytics %}
                    <!-- Vendor Analytics -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-blue-50 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-eye text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-blue-600">{{ analytics_data.analytics.total_profile_views }}</div>
                            <div class="text-sm text-blue-600">Total Profile Views</div>
                        </div>
                        <div class="bg-green-50 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-green-600">{{ analytics_data.analytics.total_inquiries }}</div>
                            <div class="text-sm text-green-600">Total Inquiries</div>
                        </div>
                        <div class="bg-purple-50 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-chart-line text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-purple-600">{{ analytics_data.analytics.overall_performance_score }}</div>
                            <div class="text-sm text-purple-600">Performance Score</div>
                        </div>
                    </div>
                    {% else %}
                    <!-- Regular User Analytics -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-chart-bar text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-blue-600">{{ analytics_data.total_activities|default:0 }}</div>
                            <div class="text-sm text-blue-600">Total Activities</div>
                        </div>
                        <div class="bg-green-50 rounded-xl p-6 text-center">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-eye text-white"></i>
                            </div>
                            <div class="text-3xl font-bold text-green-600">{{ analytics_data.profile_views_made|default:0 }}</div>
                            <div class="text-sm text-green-600">Profile Views Made</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">No analytics data available</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Enhanced Modern Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions Card -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                <div class="bg-gradient-to-r from-harrier-red to-harrier-dark p-6">
                    <h3 class="text-lg font-bold text-white flex items-center">
                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-bolt text-white text-sm"></i>
                        </div>
                        Quick Actions
                    </h3>
                </div>
                <div class="p-6 space-y-6">
                    <!-- Role Change Section -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-cog text-purple-600 text-sm"></i>
                            </div>
                            <h4 class="font-semibold text-harrier-dark">Role Management</h4>
                        </div>
                        <form method="post" class="space-y-3">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="change_role">
                            <div class="relative">
                                <select name="new_role" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200 appearance-none">
                                    <option value="customer" {% if user_profile.role == 'customer' %}selected{% endif %}>👤 Customer</option>
                                    <option value="vendor" {% if user_profile.role == 'vendor' %}selected{% endif %}>🏪 Vendor</option>
                                    <option value="admin" {% if user_profile.role == 'admin' %}selected{% endif %}>👑 Admin</option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                            <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl font-semibold hover:from-purple-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
                                <i class="fas fa-user-cog mr-2"></i>Update Role
                            </button>
                        </form>
                    </div>

                    {% if vendor %}
                    <!-- Vendor Actions Section -->
                    <div class="pt-6 border-t border-gray-100">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-building text-orange-600 text-sm"></i>
                            </div>
                            <h4 class="font-semibold text-harrier-dark">Vendor Management</h4>
                        </div>
                        <div class="space-y-3">
                            <form method="post" class="w-full">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="toggle_vendor_approval">
                                <button type="submit" class="w-full px-4 py-3 rounded-xl text-sm font-semibold transition-all duration-200 transform hover:scale-105 shadow-md
                                    {% if vendor.is_approved %}bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700{% else %}bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700{% endif %}">
                                    <i class="fas fa-{% if vendor.is_approved %}ban{% else %}shield-check{% endif %} mr-2"></i>
                                    {% if vendor.is_approved %}Revoke Approval{% else %}Approve Vendor{% endif %}
                                </button>
                            </form>

                            <a href="{% url 'core:admin_vendors' %}" class="block w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-md text-center">
                                <i class="fas fa-list mr-2"></i>View All Vendors
                            </a>

                            <!-- Vendor Stats Mini Card -->
                            <div class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-600">{{ vendor.total_sales|default:0 }}</div>
                                    <div class="text-xs text-orange-600 font-medium">Total Sales</div>
                                    <div class="flex items-center justify-center mt-2 space-x-4 text-xs text-orange-500">
                                        <span>⭐ {{ vendor.average_rating|floatformat:1|default:"N/A" }}</span>
                                        <span>📅 {{ vendor.created_at|date:"M Y" }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Account Status -->
                    <div class="pt-4 border-t">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">
                            <i class="fas fa-info-circle mr-1 text-blue-500"></i>Account Status
                        </h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                <span class="text-sm text-gray-600">Active</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if user_profile.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                    {% if user_profile.is_active %}Yes{% else %}No{% endif %}
                                </span>
                            </div>

                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                <span class="text-sm text-gray-600">Email Verified</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if user_profile.is_email_verified %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {% if user_profile.is_email_verified %}Yes{% else %}No{% endif %}
                                </span>
                            </div>

                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                <span class="text-sm text-gray-600">Staff Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if user_profile.is_staff %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {% if user_profile.is_staff %}Staff{% else %}Regular{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity Summary -->
            <div class="dashboard-card">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-heading font-bold text-harrier-dark flex items-center">
                        <i class="fas fa-clock text-blue-500 mr-2"></i>Recent Activity
                    </h3>
                </div>
                <div class="p-6">
                    {% if recent_activities %}
                        <div class="space-y-3">
                            {% for activity in recent_activities|slice:":3" %}
                            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="w-8 h-8 bg-harrier-red rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-circle text-white text-xs"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-harrier-dark truncate">{{ activity.get_action_display }}</div>
                                    <div class="text-xs text-gray-500">{{ activity.timestamp|timesince }} ago</div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if recent_activities.count > 3 %}
                        <div class="mt-4 text-center">
                            <button type="button" onclick="document.querySelector('[data-tab=\"activity\"]').click()"
                                    class="text-sm text-harrier-red hover:text-harrier-dark font-medium">
                                View All Activity
                            </button>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history text-gray-400 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-500">No recent activity</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Password Reset Modal -->
    <div id="passwordResetModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-harrier-dark">Reset Password</h3>
                <button type="button" onclick="closePasswordResetModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="mb-6">
                <p class="text-gray-600">Are you sure you want to reset the password for <strong>{{ user_profile.username }}</strong>?</p>
                <p class="text-sm text-yellow-600 mt-2">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    A new random password will be generated and displayed.
                </p>
            </div>
            <div class="flex space-x-4">
                <button type="button" onclick="closePasswordResetModal()" class="form-button-secondary flex-1">Cancel</button>
                <form method="post" class="flex-1">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="reset_password">
                    <button type="submit" class="form-button-danger w-full">Reset Password</button>
                </form>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<style>
/* Tab Styles */
.profile-tab {
    @apply py-3 px-6 text-sm font-medium text-gray-500 hover:text-gray-700
           hover:border-gray-300 whitespace-nowrap border-b-2 border-transparent
           transition-all duration-200 font-montserrat;
}

.profile-tab.active {
    @apply text-harrier-red border-harrier-red font-bold;
}

.tab-content {
    @apply hidden;
}

.tab-content.active {
    @apply block animate-fade-in-up;
}

/* Enhanced form styling for admin */
.form-section {
    @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-gray-100 mb-6 shadow-sm;
}

.form-section:hover {
    @apply shadow-md border-gray-200;
}

/* Modern Tab Styles */
.modern-tab {
    @apply flex-1 min-w-0 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300
           bg-gray-50 text-gray-600 hover:bg-gray-100 hover:text-gray-800
           border border-gray-200 hover:border-gray-300 cursor-pointer;
}

.modern-tab.active {
    @apply bg-gradient-to-r from-harrier-red to-harrier-dark text-white
           border-harrier-red shadow-lg transform scale-105;
}

.modern-tab:hover {
    @apply shadow-md transform scale-102;
}

.modern-tab.active .w-8 {
    @apply bg-white/20 text-white;
}

.modern-tab.active .text-gray-500 {
    @apply text-white/80;
}

/* Enhanced Card Styles */
.dashboard-card-modern {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100
           hover:shadow-xl transition-all duration-300 overflow-hidden;
}

.dashboard-card-modern:hover {
    @apply transform scale-102;
}

/* Stats Card Animation */
.stats-card {
    @apply transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl;
}

.stats-card:hover .stats-icon {
    @apply transform scale-110 rotate-12;
}

.stats-icon {
    @apply transition-all duration-300;
}

/* Modern Button Hover Effects */
.btn-modern {
    @apply transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.btn-modern:active {
    @apply scale-95;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Mobile responsive tabs */
@media (max-width: 768px) {
    .profile-tab, .modern-tab {
        @apply px-3 py-2 text-xs;
    }

    .modern-tab .text-left {
        @apply text-center;
    }

    .modern-tab .w-8 {
        @apply w-6 h-6;
    }

    nav[aria-label="Tabs"] {
        @apply space-x-2 overflow-x-auto;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeFileUploads();
    initializeModernEffects();
});

function openPasswordResetModal() {
    document.getElementById('passwordResetModal').classList.remove('hidden');
}

function closePasswordResetModal() {
    document.getElementById('passwordResetModal').classList.add('hidden');
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        location.reload();
    }
}

// Enhanced Tab functionality for both legacy and modern tabs
function initializeTabs() {
    const tabs = document.querySelectorAll('.profile-tab, .modern-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs and contents
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');

                // Add smooth scroll to content for better UX
                targetContent.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }
        });
    });
}

// Initialize modern effects
function initializeModernEffects() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.dashboard-card-modern, .stats-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Add click ripple effect to buttons
    const buttons = document.querySelectorAll('.btn-modern, .modern-tab');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// File upload functionality
function initializeFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update preview if it exists
                    const preview = document.getElementById('profileImagePreview');
                    if (preview && input.name === 'profile_picture') {
                        if (preview.tagName === 'IMG') {
                            preview.src = e.target.result;
                        } else {
                            // Replace div with img
                            const img = document.createElement('img');
                            img.className = preview.className;
                            img.src = e.target.result;
                            img.alt = 'Profile picture';
                            img.id = 'profileImagePreview';
                            preview.parentNode.replaceChild(img, preview);
                        }
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    });
}
</script>
{% endblock %}
